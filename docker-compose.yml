version: '3.8'

networks:
  mions-net:
    external: true

services:
  app:
    build:
      context: ./service.srv  # Define o diretório onde está o seu Dockerfile
      dockerfile: Dockerfile   # Se o seu Dockerfile tiver um nome específico, como 'Dockerfile', não precisa mudar
    volumes:
      - ./service.srv:/srv
    expose:
      - "9000"
    networks:
      - mions-net

  srv:
    image: nginx:alpine
    volumes:
      - ./service.srv:/srv
      - ./service.srv/srv.conf:/etc/nginx/conf.d/srv.conf
      - ./service.srv/nginx.conf:/etc/nginx/nginx.conf
    expose:
      - 8080
    ports:
      - "127.0.0.1:8085:8080"
    networks:
      - mions-net
