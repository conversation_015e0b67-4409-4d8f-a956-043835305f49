import pymysql
from sshtunnel import SSHTunnelForwarder
from datetime import datetime
from collections import Counter
import time
import csv
import os

# Configuração do loop automático
AUTO_RUN_INTERVAL = 300  # 5 minutos em segundos
RUN_ONCE = False  # Mude para True se quiser executar apenas uma vez

# Função para exibir logs com timestamp
def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

# Função para mapear status do Banco B para o formato do Banco A
def map_status_b_to_a(status_b):
    """
    Mapeia o status do Banco B para o formato aceito pelo Banco A.
    Converte para uppercase antes do mapeamento.

    Args:
        status_b (str): Status original do Banco B

    Returns:
        str: Status mapeado para o Banco A
    """
    # Converter para uppercase e remover espaços
    status_upper = str(status_b).upper().strip()

    # Dicionário de mapeamento de status
    status_mapping = {
        "RECEIVED":              "RECEIVED",
        "CLOSED":                "CLOSED",
        "CANCELED":              "CANCELED",
        "SHIPPED":               "SHIPPED",
        "SHIPPING":              "SHIPPING",
        "REFUNDED":              "REFUNDED",
        "REFUNDING":             "REFUNDING",
        "REFUSED":               "REFUSED",
        "CANCELLED":             "CANCELED",  # Spelling correction
        "COMMITED":              "COMMITED",
        "WAITINGACCEPTANCE":     "SHIPPING",  # Fallback
        "TO_COLLECT":            "TO_COLLECT",
        "PROCESSED":             "SHIPPING",  # Fallback
        "FINISH":                "CLOSED",    # Mapping to CLOSED
        "SENT":                  "SHIPPED",   # Mapping to SHIPPED
        "STAGING":               "SHIPPING",  # Fallback
        "WAITING_ACCEPTANCE":    "SHIPPING",  # Fallback
        "TOSHIP":                "SHIPPING",  # Fallback
        "PREPARATION":           "SHIPPING",  # Corrected from PREPARATION to SHIPPING
        "WAITING_DEBIT_PAYMENT": "SHIPPING",  # Fallback
        "INPREPARATION":         "SHIPPING",  # Corrected from PREPARATION to SHIPPING
        "NEED_TO_BE_SENT":       "SHIPPING",  # Fallback
    }

    # Buscar o status mapeado
    mapped_status = status_mapping.get(status_upper, status_upper)

    # Log do mapeamento se houve alteração
    if mapped_status != status_upper:
        log(f"Status mapeado: '{status_b}' -> '{status_upper}' -> '{mapped_status}'", "MAPPING")

    return mapped_status

# Definir variáveis para o Banco B (origem)
SSH_USER_B = "root"
SSH_PASS_B = "fF[8Q%C[WR]4!UDW"
SSH_HOST_B = "************"
SSH_PORT_B = 22
TUNNEL_PORT_B = 13310
DB_HOST_B = "127.0.0.1"
DB_PORT_B = 3306
DB_USER_B = "connect"
DB_PASS_B = "J%HR6Bj}w4lgQs@.GJ:Bnr~34-?10v^O"
DB_NAME_B = "connectbig"

# Definir variáveis para o Banco Accounts (destino)
DB_ACC_HOST = "**************"
DB_ACC_PORT = 3306
DB_ACC_USER = "hublord"
DB_ACC_PASS = "sYR9*z#G0fJ1,ONr4wS}xEka9WRad8r8"
DB_ACC_NAME = "db_accounts"

# Definir data inicial
START_DATE = "2025-01-01"

def run_update_process():
    """Função principal que executa o processo de atualização"""
    # Definir nome do arquivo CSV para pedidos não encontrados
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    MISSING_ORDERS_CSV = f"pedidos_nao_encontrados_{timestamp}.csv"

    log("Iniciando processo de atualização de status de pedidos")
    log(f"Data de início para busca de pedidos: {START_DATE}")
    
    process_start_time = time.time()

    try:
        log("Iniciando túnel SSH para o Banco B (origem)...")
        with SSHTunnelForwarder(
            (SSH_HOST_B, SSH_PORT_B),
            ssh_username=SSH_USER_B,
            ssh_password=SSH_PASS_B,
            remote_bind_address=(DB_HOST_B, DB_PORT_B),
            local_bind_address=("127.0.0.1", TUNNEL_PORT_B)
        ) as tunnel_B:
            log(f"Túnel SSH para Banco B estabelecido (localhost:{TUNNEL_PORT_B} -> {SSH_HOST_B}:{SSH_PORT_B})")
            
            # Variáveis para controlar estado das conexões
            connection_B = None
            connection_acc = None
            
            try:
                log(f"Conectando ao banco de dados de origem (Banco B)...")
                connection_B = pymysql.connect(
                    host="127.0.0.1",
                    port=TUNNEL_PORT_B,
                    user=DB_USER_B,
                    password=DB_PASS_B,
                    database=DB_NAME_B
                )
                log("Conexão com Banco B (origem) estabelecida com sucesso")
                
                log(f"Conectando ao banco de dados de destino (Accounts) em {DB_ACC_HOST}...")
                connection_acc = pymysql.connect(
                    host=DB_ACC_HOST,
                    port=DB_ACC_PORT,
                    user=DB_ACC_USER,
                    password=DB_ACC_PASS,
                    database=DB_ACC_NAME
                )
                log("Conexão com Banco Accounts (destino) estabelecida com sucesso")
                
                cursor_B = connection_B.cursor()
                cursor_acc = connection_acc.cursor()
                
                # Obter pedidos do Banco B (origem)
                log("Construindo query para buscar pedidos no Banco B...")
                sql_query_B = """
                    SELECT o.order_id, o.status, m.name as marketplace_name
                    FROM orders o
                    JOIN marketplaces m ON o.marketplace_id = m.id
                    WHERE o.created_at >= %s
                    ORDER BY o.created_at DESC;
                """
                
                log(f"Executando query no Banco B para buscar pedidos desde {START_DATE}...")
                cursor_B.execute(sql_query_B, (START_DATE,))
                results_B = cursor_B.fetchall()
                
                orders_B = [
                    {
                        "order_id": row[0],
                        "status": row[1],
                        "status_mapped": map_status_b_to_a(row[1]),
                        "marketplace_name": row[2]
                    }
                    for row in results_B
                ]
                
                log(f"Encontrados {len(orders_B)} pedidos no Banco B (origem)")
                
                # Estatísticas para acompanhamento
                found_count = 0
                not_found_count = 0
                update_needed_count = 0
                update_success_count = 0
                update_failed_count = 0
                no_update_needed_count = 0
                
                orders_not_found = []
                
                log("Iniciando verificação e atualização de status...")
                total_orders = len(orders_B)
                
                # Para mostrar progresso
                progress_interval = max(1, total_orders // 20)
                start_time = time.time()
                
                # Verificar cada pedido no Banco Accounts (destino)
                for i, order_B in enumerate(orders_B):
                    # Mostrar progresso
                    if (i+1) % progress_interval == 0 or i+1 == total_orders:
                        progress_pct = ((i+1) / total_orders) * 100
                        elapsed_time = time.time() - start_time
                        log(f"Progresso: {i+1}/{total_orders} pedidos verificados ({progress_pct:.1f}%)")
                    
                    # Buscar o pedido no banco de destino pela coluna reference
                    query_acc = """
                        SELECT id, reference, state, channel
                        FROM tbl_orders
                        WHERE reference = %s
                    """
                    
                    cursor_acc.execute(query_acc, (order_B["order_id"],))
                    result_acc = cursor_acc.fetchone()
                    
                    # Verificar se o pedido foi encontrado
                    if result_acc:
                        found_count += 1
                        acc_id, acc_reference, acc_state, acc_channel = result_acc
                        
                        # Verificar se o status precisa ser atualizado (usando status mapeado)
                        mapped_status = order_B["status_mapped"]
                        if acc_state != mapped_status:
                            update_needed_count += 1

                            # Executar a atualização do status
                            try:
                                update_query = """
                                    UPDATE tbl_orders
                                    SET state = %s,
                                        updated_at = NOW()
                                    WHERE id = %s
                                """
                                cursor_acc.execute(update_query, (mapped_status, acc_id))
                                connection_acc.commit()
                                update_success_count += 1

                                log(f"Pedido {acc_reference} atualizado: {acc_state} -> {mapped_status}", "UPDATE")

                            except Exception as e:
                                update_failed_count += 1
                                log(f"Erro ao atualizar pedido {acc_reference}: {str(e)}", "ERRO")
                        else:
                            no_update_needed_count += 1
                    else:
                        not_found_count += 1
                        orders_not_found.append(order_B)
                
                total_time = time.time() - process_start_time
                
                # Exibir resumo
                log("\n----- RESUMO DA ATUALIZAÇÃO -----", "RESULTADO")
                log(f"Total de pedidos verificados: {len(orders_B)}", "RESULTADO")
                log(f"Pedidos encontrados: {found_count} ({(found_count/len(orders_B))*100:.1f}%)", "RESULTADO")
                log(f"Pedidos NÃO encontrados: {not_found_count} ({(not_found_count/len(orders_B))*100:.1f}%)", "RESULTADO")
                log(f"Atualizações realizadas: {update_success_count}", "RESULTADO")
                log(f"Atualizações com falha: {update_failed_count}", "RESULTADO")
                log(f"Pedidos já sincronizados: {no_update_needed_count}", "RESULTADO")
                log(f"Tempo de processamento: {total_time:.2f} segundos", "RESULTADO")
                
                # Fechar cursores
                cursor_B.close()
                cursor_acc.close()
                
            except Exception as e:
                log(f"Erro durante execução: {str(e)}", "ERRO")
                import traceback
                log(traceback.format_exc(), "ERRO")
                return False
            
            finally:
                # Fechar conexões corretamente
                if connection_B is not None and connection_B.open:
                    try:
                        connection_B.close()
                        log("Conexão com Banco B encerrada")
                    except Exception as e:
                        log(f"Erro ao fechar conexão B: {str(e)}", "ERRO")
                
                if connection_acc is not None and hasattr(connection_acc, 'open') and connection_acc.open:
                    try:
                        connection_acc.close()
                        log("Conexão com Banco Accounts encerrada")
                    except Exception as e:
                        log(f"Erro ao fechar conexão Accounts: {str(e)}", "ERRO")

    except Exception as e:
        log(f"Erro ao configurar túnel SSH: {str(e)}", "ERRO")
        import traceback
        log(traceback.format_exc(), "ERRO")
        return False
    
    return True

# Função principal com loop automático
def main():
    log("=== SCRIPT DE ATUALIZAÇÃO AUTOMÁTICA DE STATUS ===")
    log(f"Configurado para executar a cada {AUTO_RUN_INTERVAL} segundos ({AUTO_RUN_INTERVAL//60} minutos)")
    log("Pressione Ctrl+C para parar o script")
    
    execution_count = 0
    
    try:
        while True:
            execution_count += 1
            log(f"\n--- EXECUÇÃO #{execution_count} ---")
            
            success = run_update_process()
            
            if success:
                log("Processo concluído com sucesso")
            else:
                log("Processo concluído com erros")
            
            if RUN_ONCE:
                log("Configurado para executar apenas uma vez. Finalizando...")
                break
            
            log(f"Aguardando {AUTO_RUN_INTERVAL} segundos para próxima execução...")
            log(f"Próxima execução em: {datetime.fromtimestamp(time.time() + AUTO_RUN_INTERVAL).strftime('%Y-%m-%d %H:%M:%S')}")
            
            time.sleep(AUTO_RUN_INTERVAL)
            
    except KeyboardInterrupt:
        log("\nScript interrompido pelo usuário (Ctrl+C)")
    except Exception as e:
        log(f"Erro inesperado: {str(e)}", "ERRO")
    finally:
        log("Script finalizado")

if __name__ == "__main__":
    main()
