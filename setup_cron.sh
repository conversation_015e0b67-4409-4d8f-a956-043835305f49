#!/bin/bash

# Script para configurar execução automática do update_status.py a cada 5 minutos

echo "Configurando execução automática do update_status.py a cada 5 minutos..."

# Caminho completo para o script
SCRIPT_PATH="/home/<USER>/Projects/giip/update_status/update_status.py"
LOG_PATH="/home/<USER>/Projects/giip/update_status/logs"

# Criar diretório de logs se não existir
mkdir -p "$LOG_PATH"

# Verificar se o script existe
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "ERRO: Script não encontrado em $SCRIPT_PATH"
    exit 1
fi

# Backup do crontab atual
echo "Fazendo backup do crontab atual..."
crontab -l > crontab_backup_$(date +%Y%m%d_%H%M%S).txt 2>/dev/null || echo "Nenhum crontab existente para backup"

# Criar entrada do crontab
CRON_ENTRY="*/5 * * * * cd /home/<USER>/Projects/giip/update_status && /usr/bin/python3 update_status.py >> $LOG_PATH/update_status_$(date +\%Y\%m\%d).log 2>&1"

# Adicionar ao crontab
echo "Adicionando entrada ao crontab..."
(crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -

echo "Configuração concluída!"
echo ""
echo "O script update_status.py agora será executado a cada 5 minutos."
echo "Logs serão salvos em: $LOG_PATH/"
echo ""
echo "Para verificar o crontab: crontab -l"
echo "Para remover: crontab -e (e deletar a linha)"
echo "Para ver logs: tail -f $LOG_PATH/update_status_$(date +%Y%m%d).log"
