# Script de Sincronização de Status de Pedidos

## 📋 Visão Geral

O script `update_status.py` é uma ferramenta de sincronização que atualiza automaticamente os status de pedidos entre dois bancos de dados diferentes. Ele conecta-se a um banco de dados de origem (via túnel SSH) e sincroniza os status dos pedidos com um banco de dados de destino.

## 🎯 Objetivo Principal

**Sincronizar status de pedidos** entre:
- **Banco de Origem**: `connectbig` (via SSH tunnel)
- **Banco de Destino**: `db_accounts` (conexão direta)

## 🔧 Funcionalidades

### 🔄 Processo de Sincronização

1. **Conexão Segura**: Estabelece túnel SSH para acessar banco remoto
2. **Busca de Pedidos**: Obtém pedidos desde 01/01/2025 do banco origem
3. **Mapeamento de Status**: Converte status do Banco B para formato do Banco A
4. **Verificação**: Compara status mapeado entre origem e destino
5. **Atualização**: Sincroniza status quando necessário
6. **Relatório**: Gera estatísticas detalhadas do processo

### 📊 Análises Incluídas

- **Distribuição por Marketplace**: Quantos pedidos por plataforma
- **Taxa de Sincronização**: Percentual de pedidos encontrados
- **Estatísticas de Atualização**: Sucessos, falhas e já sincronizados
- **Pedidos Não Encontrados**: Lista de pedidos ausentes no destino
- **Mapeamento de Status**: Quantos status foram convertidos vs mantidos originais

## 🗄️ Estrutura dos Bancos

### Banco de Origem (connectbig)
```sql
-- Tabela: orders
-- Campos: order_id, status, marketplace_id, created_at
-- Join com: marketplaces (para obter nome do marketplace)
```

### Banco de Destino (db_accounts)
```sql
-- Tabela: tbl_orders  
-- Campos: id, reference, state, channel, updated_at
-- Campo 'reference' corresponde ao 'order_id' da origem
```

## 🔐 Configurações de Conexão

### SSH Tunnel (Banco Origem)
- **Host**: ************
- **Porta**: 22
- **Túnel Local**: 13310
- **Banco**: connectbig

### Conexão Direta (Banco Destino)
- **Host**: **************
- **Porta**: 3306
- **Banco**: db_accounts

## 📈 Relatórios Gerados

### 📊 Estatísticas em Tempo Real
```
[2025-01-15 10:30:15] [INFO] Encontrados 1,250 pedidos no Banco B (origem)
[2025-01-15 10:30:16] [INFO] Distribuição de pedidos por marketplace:
[2025-01-15 10:30:16] [INFO] - Amazon: 450 pedidos
[2025-01-15 10:30:16] [INFO] - eBay: 320 pedidos
[2025-01-15 10:30:16] [INFO] - Carrefour: 280 pedidos
```

### 📋 Resumo Final
```
----- RESUMO DA ATUALIZAÇÃO -----
Total de pedidos verificados: 1,250
Pedidos encontrados no banco destino: 1,180 (94.4%)
Pedidos NÃO encontrados no banco destino: 70 (5.6%)
Pedidos que necessitavam atualização: 45 (3.6%)
Atualizações realizadas com sucesso: 45
Atualizações com falha: 0
Pedidos já sincronizados: 1,135 (90.8%)

----- ESTATÍSTICAS DE MAPEAMENTO -----
Status que foram mapeados: 320 (25.6%)
Status mantidos originais: 930 (74.4%)
```

### 📄 Arquivo CSV
- **Nome**: `pedidos_nao_encontrados_YYYYMMDD_HHMMSS.csv`
- **Conteúdo**: Lista de pedidos não encontrados no banco destino
- **Campos**: order_id, status, marketplace_name

## 🔄 Sistema de Mapeamento de Status

### 📋 Função de Mapeamento
O script inclui uma função `map_status_b_to_a()` que:
- **Converte para UPPERCASE**: Normaliza todos os status
- **Remove espaços**: Limpa formatação inconsistente
- **Mapeia status**: Converte para formato aceito pelo Banco A
- **Log detalhado**: Registra todas as conversões realizadas

### 🗺️ Tabela de Mapeamento
```python
status_mapping = {
    "RECEIVED":              "RECEIVED",
    "CLOSED":                "CLOSED",
    "CANCELED":              "CANCELED",
    "SHIPPED":               "SHIPPED",
    "SHIPPING":              "SHIPPING",
    "REFUNDED":              "REFUNDED",
    "REFUNDING":             "REFUNDING",
    "REFUSED":               "REFUSED",
    "CANCELLED":             "CANCELED",  # Spelling correction
    "COMMITED":              "COMMITED",
    "WAITINGACCEPTANCE":     "SHIPPING",  # Fallback
    "TO_COLLECT":            "TO_COLLECT",
    "PROCESSED":             "SHIPPING",  # Fallback
    "FINISH":                "CLOSED",    # Mapping to CLOSED
    "SENT":                  "SHIPPED",   # Mapping to SHIPPED
    "STAGING":               "SHIPPING",  # Fallback
    "WAITING_ACCEPTANCE":    "SHIPPING",  # Fallback
    "TOSHIP":                "SHIPPING",  # Fallback
    "PREPARATION":           "SHIPPING",  # Corrected from PREPARATION to SHIPPING
    "WAITING_DEBIT_PAYMENT": "SHIPPING",  # Fallback
    "INPREPARATION":         "SHIPPING",  # Corrected from PREPARATION to SHIPPING
    "NEED_TO_BE_SENT":       "SHIPPING",  # Fallback
}
```

### 📊 Exemplos de Mapeamento
- `"ToShip"` → `"TOSHIP"` → `"SHIPPING"`
- `"InPreparation"` → `"INPREPARATION"` → `"SHIPPING"`
- `"Cancelled"` → `"CANCELLED"` → `"CANCELED"`
- `"FINISH"` → `"FINISH"` → `"CLOSED"`
- `"SENT"` → `"SENT"` → `"SHIPPED"`

## ⚡ Características Técnicas

### 🔒 Segurança
- **SSH Tunnel**: Conexão criptografada para banco remoto
- **Credenciais**: Armazenadas em variáveis (considerar usar .env)
- **Transações**: Commits automáticos após cada atualização

### 📊 Performance
- **Progresso**: Indicador de progresso a cada 5%
- **Tempo Estimado**: Cálculo de tempo restante
- **Otimização**: Processamento sequencial com feedback
- **Mapeamento**: Conversão de status em tempo real

### 🛡️ Tratamento de Erros
- **Try/Catch**: Captura e log de todos os erros
- **Conexões**: Fechamento seguro de todas as conexões
- **Rollback**: Cada atualização é individual (sem transações em lote)

## 🚀 Como Executar

### Pré-requisitos
```bash
pip install pymysql sshtunnel
```

### Execução
```bash
python update_status.py
```

### Logs
O script gera logs detalhados com timestamp:
```
[2025-01-15 10:30:15] [INFO] Iniciando script de atualização
[2025-01-15 10:30:16] [MAPPING] Status mapeado: 'ToShip' -> 'TOSHIP' -> 'SHIPPING'
[2025-01-15 10:30:16] [UPDATE] Pedido 12345-A atualizado: PENDING -> SHIPPING (original: ToShip)
[2025-01-15 10:30:17] [MISSING] Pedido 67890-B não encontrado no banco destino
```

## 📁 Arquivos Gerados

### CSV de Pedidos Não Encontrados
```csv
order_id,status,marketplace_name
12345-A,PENDING,Amazon
67890-B,SHIPPED,eBay
```

## ⚠️ Considerações Importantes

### 🔄 Sincronização
- **Direção**: Apenas origem → destino
- **Critério**: Campo `reference` = `order_id`
- **Atualização**: Apenas quando status diferem

### 📅 Período
- **Data Inicial**: 01/01/2025 (configurável)
- **Ordenação**: Pedidos mais recentes primeiro

### 🎯 Marketplaces
- **Inclusão**: Todos os marketplaces por padrão
- **Filtros**: Possível excluir marketplaces específicos

## 🔧 Possíveis Melhorias

1. **Configuração Externa**: Usar arquivo .env para credenciais
2. **Processamento em Lote**: Agrupar atualizações para melhor performance
3. **Agendamento**: Integrar com cron/scheduler para execução automática
4. **Notificações**: Enviar relatórios por email
5. **Backup**: Criar backup antes das atualizações
6. **Logs Estruturados**: Usar logging framework profissional

## 📞 Suporte

Para dúvidas ou problemas:
- Verificar logs detalhados gerados pelo script
- Analisar arquivo CSV de pedidos não encontrados
- Verificar conectividade SSH e credenciais de banco
