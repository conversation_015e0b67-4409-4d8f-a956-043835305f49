import pymysql
from sshtunnel import SSHTunnelForwarder
from datetime import datetime
from collections import Counter
import time
import csv
import os

# Função para exibir logs com timestamp
def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

# Função para mapear status do Banco B para o formato do Banco A
def map_status_b_to_a(status_b):
    """
    Mapeia o status do Banco B para o formato aceito pelo Banco A.
    Converte para uppercase antes do mapeamento.

    Args:
        status_b (str): Status original do Banco B

    Returns:
        str: Status mapeado para o Banco A
    """
    # Converter para uppercase e remover espaços
    status_upper = str(status_b).upper().strip()

    # Dicionário de mapeamento de status
    status_mapping = {
        "RECEIVED":              "RECEIVED",
        "CLOSED":                "CLOSED",
        "CANCELED":              "CANCELED",
        "SHIPPED":               "SHIPPED",
        "SHIPPING":              "SHIPPING",
        "REFUNDED":              "REFUNDED",
        "REFUNDING":             "REFUNDING",
        "REFUSED":               "REFUSED",
        "CANCELLED":             "CANCELED",  # Spelling correction
        "COMMITED":              "COMMITED",
        "WAITINGACCEPTANCE":     "SHIPPING",  # Fallback
        "TO_COLLECT":            "TO_COLLECT",
        "PROCESSED":             "SHIPPING",  # Fallback
        "FINISH":                "CLOSED",    # Mapping to CLOSED
        "SENT":                  "SHIPPED",   # Mapping to SHIPPED
        "STAGING":               "SHIPPING",  # Fallback
        "WAITING_ACCEPTANCE":    "SHIPPING",  # Fallback
        "TOSHIP":                "SHIPPING",  # Fallback
        "PREPARATION":           "SHIPPING",  # Corrected from PREPARATION to SHIPPING
        "WAITING_DEBIT_PAYMENT": "SHIPPING",  # Fallback
        "INPREPARATION":         "SHIPPING",  # Corrected from PREPARATION to SHIPPING
        "NEED_TO_BE_SENT":       "SHIPPING",  # Fallback
    }

    # Buscar o status mapeado
    mapped_status = status_mapping.get(status_upper, status_upper)

    # Log do mapeamento se houve alteração
    if mapped_status != status_upper:
        log(f"Status mapeado: '{status_b}' -> '{status_upper}' -> '{mapped_status}'", "MAPPING")

    return mapped_status

# Definir variáveis para o Banco B (origem)
SSH_USER_B = "root"
SSH_PASS_B = "fF[8Q%C[WR]4!UDW"
SSH_HOST_B = "************"
SSH_PORT_B = 22
TUNNEL_PORT_B = 13310
DB_HOST_B = "127.0.0.1"
DB_PORT_B = 3306
DB_USER_B = "connect"
DB_PASS_B = "J%HR6Bj}w4lgQs@.GJ:Bnr~34-?10v^O"
DB_NAME_B = "connectbig"

# Definir variáveis para o Banco Accounts (destino)
DB_ACC_HOST = "**************"
DB_ACC_PORT = 3306
DB_ACC_USER = "hublord"
DB_ACC_PASS = "sYR9*z#G0fJ1,ONr4wS}xEka9WRad8r8"
DB_ACC_NAME = "db_accounts"

# Definir data inicial
START_DATE = "2025-01-01"

# Configuração do loop automático
AUTO_RUN_INTERVAL = 300  # 5 minutos em segundos
RUN_ONCE = False  # Mude para True se quiser executar apenas uma vez

def run_update_process():
    """Função principal que executa o processo de atualização"""
    # Definir nome do arquivo CSV para pedidos não encontrados
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    MISSING_ORDERS_CSV = f"pedidos_nao_encontrados_{timestamp}.csv"

    log("Iniciando processo de atualização de status de pedidos")
    log(f"Data de início para busca de pedidos: {START_DATE}")

    process_start_time = time.time()

    try:
        log("Iniciando túnel SSH para o Banco B (origem)...")
        with SSHTunnelForwarder(
            (SSH_HOST_B, SSH_PORT_B),
            ssh_username=SSH_USER_B,
            ssh_password=SSH_PASS_B,
            remote_bind_address=(DB_HOST_B, DB_PORT_B),
            local_bind_address=("127.0.0.1", TUNNEL_PORT_B)
        ) as tunnel_B:
            log(f"Túnel SSH para Banco B estabelecido (localhost:{TUNNEL_PORT_B} -> {SSH_HOST_B}:{SSH_PORT_B})")

            # Variáveis para controlar estado das conexões
            connection_B = None
            connection_acc = None

            try:
            log(f"Conectando ao banco de dados de origem (Banco B)...")
            connection_B = pymysql.connect(
                host="127.0.0.1",
                port=TUNNEL_PORT_B,
                user=DB_USER_B,
                password=DB_PASS_B,
                database=DB_NAME_B
            )
            log("Conexão com Banco B (origem) estabelecida com sucesso")
            
            log(f"Conectando ao banco de dados de destino (Accounts) em {DB_ACC_HOST}...")
            connection_acc = pymysql.connect(
                host=DB_ACC_HOST,
                port=DB_ACC_PORT,
                user=DB_ACC_USER,
                password=DB_ACC_PASS,
                database=DB_ACC_NAME
            )
            log("Conexão com Banco Accounts (destino) estabelecida com sucesso")
            
            cursor_B = connection_B.cursor()
            cursor_acc = connection_acc.cursor()
            
            # Obter pedidos do Banco B (origem) - modificando a consulta para excluir os marketplaces especificados
            log("Construindo query para buscar pedidos no Banco B excluindo marketplaces específicos...")
            sql_query_B = """
                SELECT o.order_id, o.status, m.name as marketplace_name
                FROM orders o
                JOIN marketplaces m ON o.marketplace_id = m.id
                WHERE o.created_at >= %s
                ORDER BY o.created_at DESC;
            """
            
            log(f"Executando query no Banco B para buscar pedidos desde {START_DATE} (excluindo marketplaces específicos)...")
            cursor_B.execute(sql_query_B, (START_DATE,))
            results_B = cursor_B.fetchall()
            
            orders_B = [
                {
                    "order_id": row[0],
                    "status": row[1],
                    "status_mapped": map_status_b_to_a(row[1]),  # Aplicar mapeamento
                    "marketplace_name": row[2]
                }
                for row in results_B
            ]
            
            log(f"Encontrados {len(orders_B)} pedidos no Banco B (origem)")
            
            # Exibir distribuição por marketplace
            marketplace_counts = Counter([order["marketplace_name"] for order in orders_B])
            log("Distribuição de pedidos por marketplace:")
            for marketplace, count in marketplace_counts.items():
                log(f"- {marketplace}: {count} pedidos")
            
            # Estatísticas para acompanhamento
            found_count = 0
            not_found_count = 0
            update_needed_count = 0
            update_success_count = 0
            update_failed_count = 0
            no_update_needed_count = 0

            # Estatísticas de mapeamento
            mapped_status_count = 0
            original_status_count = 0
            
            # Armazenar detalhes para relatório
            orders_to_update = []
            orders_not_found = []
            
            log("Iniciando verificação e atualização de status em ambos os bancos...")
            total_orders = len(orders_B)
            
            # Para mostrar progresso
            progress_interval = max(1, total_orders // 20)  # Mostrar progresso a cada 5%
            start_time = time.time()
            
            # Verificar cada pedido no Banco Accounts (destino)
            for i, order_B in enumerate(orders_B):
                # Contar mapeamentos de status
                if order_B["status"] != order_B["status_mapped"]:
                    mapped_status_count += 1
                else:
                    original_status_count += 1
                # Mostrar progresso
                if (i+1) % progress_interval == 0 or i+1 == total_orders:
                    progress_pct = ((i+1) / total_orders) * 100
                    elapsed_time = time.time() - start_time
                    remaining_orders = total_orders - (i+1)
                    
                    if remaining_orders > 0 and i > 0:
                        time_per_order = elapsed_time / (i+1)
                        estimated_remaining = time_per_order * remaining_orders
                        log(f"Progresso: {i+1}/{total_orders} pedidos verificados ({progress_pct:.1f}%) - Tempo estimado restante: {estimated_remaining:.1f}s")
                    else:
                        log(f"Progresso: {i+1}/{total_orders} pedidos verificados ({progress_pct:.1f}%)")
                
                # Buscar o pedido no banco de destino pela coluna reference
                query_acc = """
                    SELECT id, reference, state, channel
                    FROM tbl_orders
                    WHERE reference = %s
                """
                
                cursor_acc.execute(query_acc, (order_B["order_id"],))
                result_acc = cursor_acc.fetchone()
                
                # Verificar se o pedido foi encontrado
                if result_acc:
                    found_count += 1
                    acc_id, acc_reference, acc_state, acc_channel = result_acc
                    
                    # Verificar se o status precisa ser atualizado (usando status mapeado)
                    mapped_status = order_B["status_mapped"]
                    if acc_state != mapped_status:
                        update_needed_count += 1

                        # Armazenar informações para o relatório
                        order_info = {
                            "id": acc_id,
                            "reference": acc_reference,
                            "current_state": acc_state,
                            "original_status": order_B["status"],
                            "new_state": mapped_status,
                            "channel": acc_channel,
                            "marketplace": order_B["marketplace_name"]
                        }
                        orders_to_update.append(order_info)

                        # Executar a atualização do status
                        try:
                            update_query = """
                                UPDATE tbl_orders
                                SET state = %s,
                                    updated_at = NOW()
                                WHERE id = %s
                            """
                            cursor_acc.execute(update_query, (mapped_status, acc_id))
                            connection_acc.commit()
                            update_success_count += 1

                            # Log detalhado da atualização
                            if order_B["status"] != mapped_status:
                                log(f"Pedido {acc_reference} atualizado: {acc_state} -> {mapped_status} (original: {order_B['status']})", "UPDATE")
                            else:
                                log(f"Pedido {acc_reference} atualizado: {acc_state} -> {mapped_status}", "UPDATE")

                        except Exception as e:
                            update_failed_count += 1
                            log(f"Erro ao atualizar pedido {acc_reference}: {str(e)}", "ERRO")
                            order_info["error"] = str(e)
                    else:
                        no_update_needed_count += 1
                else:
                    not_found_count += 1
                    orders_not_found.append(order_B)
                    log(f"Pedido {order_B['order_id']} não encontrado no banco destino", "MISSING")
            
            total_time = time.time() - start_time
            log(f"Processo concluído em {total_time:.2f} segundos")
            
            # Exportar pedidos não encontrados para CSV
            if orders_not_found:
                log(f"Exportando {len(orders_not_found)} pedidos não encontrados para CSV: {MISSING_ORDERS_CSV}")
                try:
                    with open(MISSING_ORDERS_CSV, 'w', newline='', encoding='utf-8') as csvfile:
                        fieldnames = ["order_id", "status", "marketplace_name"]
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        
                        for order in orders_not_found:
                            writer.writerow(order)
                            
                    log(f"Arquivo CSV criado com sucesso: {os.path.abspath(MISSING_ORDERS_CSV)}")
                except Exception as e:
                    log(f"Erro ao criar arquivo CSV: {str(e)}", "ERRO")
            else:
                log("Não há pedidos não encontrados para exportar para CSV")
            
            # Exibir resumo
            log("\n----- RESUMO DA ATUALIZAÇÃO -----", "RESULTADO")
            log(f"Total de pedidos verificados: {len(orders_B)}", "RESULTADO")
            log(f"Pedidos encontrados no banco destino: {found_count} ({(found_count/len(orders_B))*100:.1f}%)", "RESULTADO")
            log(f"Pedidos NÃO encontrados no banco destino: {not_found_count} ({(not_found_count/len(orders_B))*100:.1f}%)", "RESULTADO")
            log(f"Pedidos que necessitavam atualização: {update_needed_count} ({(update_needed_count/len(orders_B))*100:.1f}%)", "RESULTADO")
            log(f"Atualizações realizadas com sucesso: {update_success_count}", "RESULTADO")
            log(f"Atualizações com falha: {update_failed_count}", "RESULTADO")
            log(f"Pedidos já sincronizados: {no_update_needed_count} ({(no_update_needed_count/len(orders_B))*100:.1f}%)", "RESULTADO")

            # Estatísticas de mapeamento de status
            log("\n----- ESTATÍSTICAS DE MAPEAMENTO -----", "RESULTADO")
            log(f"Status que foram mapeados: {mapped_status_count} ({(mapped_status_count/len(orders_B))*100:.1f}%)", "RESULTADO")
            log(f"Status mantidos originais: {original_status_count} ({(original_status_count/len(orders_B))*100:.1f}%)", "RESULTADO")
            
            # Análise por marketplace para pedidos não encontrados
            if not_found_count > 0:
                missing_by_marketplace = Counter([order["marketplace_name"] for order in orders_not_found])
                log("\nPedidos não encontrados por marketplace:", "ANÁLISE")
                for marketplace, count in missing_by_marketplace.items():
                    total_missing = sum(missing_by_marketplace.values())
                    percentage = (count / total_missing) * 100
                    log(f"- {marketplace}: {count} pedidos ({percentage:.1f}%)", "ANÁLISE")
                
                # Indicar onde está o arquivo CSV de pedidos não encontrados
                log(f"\nArquivo CSV com pedidos não encontrados salvo em: {os.path.abspath(MISSING_ORDERS_CSV)}", "ARQUIVO")
            
            # Análise por marketplace para pedidos atualizados
            if update_success_count > 0:
                update_by_marketplace = Counter([order["marketplace"] for order in orders_to_update])
                log("\nPedidos atualizados por marketplace:", "ANÁLISE")
                for marketplace, count in update_by_marketplace.items():
                    total_updates = sum(update_by_marketplace.values())
                    percentage = (count / total_updates) * 100
                    log(f"- {marketplace}: {count} pedidos ({percentage:.1f}%)", "ANÁLISE")
            
            log("\n[IMPORTANTE] As atualizações foram realizadas no banco de dados.", "AVISO")
            
            # Fechar cursores
            cursor_B.close()
            cursor_acc.close()
            
        except Exception as e:
            log(f"Erro durante execução: {str(e)}", "ERRO")
            import traceback
            log(traceback.format_exc(), "ERRO")
        
        finally:
            # Fechar conexões corretamente
            if connection_B is not None and connection_B.open:
                try:
                    connection_B.close()
                    log("Conexão com Banco B encerrada")
                except Exception as e:
                    log(f"Erro ao fechar conexão B: {str(e)}", "ERRO")
            
            if connection_acc is not None and hasattr(connection_acc, 'open') and connection_acc.open:
                try:
                    connection_acc.close()
                    log("Conexão com Banco Accounts encerrada")
                except Exception as e:
                    log(f"Erro ao fechar conexão Accounts: {str(e)}", "ERRO")
            
            log("Todas as conexões com os bancos foram encerradas")

except Exception as e:
    log(f"Erro ao configurar túnel SSH: {str(e)}", "ERRO")
    import traceback
    log(traceback.format_exc(), "ERRO")

log("Script finalizado")