import pymysql
from sshtunnel import SSHTunnelForwarder
from datetime import datetime
import json
import re

# Função para exibir logs com timestamp
def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

# Função para extrair NIF do response_first JSON
def extract_nif_from_response(response_first):
    """
    Extrai o NIF (value) do campo response_first JSON.
    Procura por order_additional_fields com code 'vatid' e retorna o value.
    """
    try:
        if not response_first:
            return None

        # Se response_first for string, fazer parse JSON
        if isinstance(response_first, str):
            data = json.loads(response_first)
        else:
            data = response_first

        # Procurar em order_additional_fields
        additional_fields = data.get('order_additional_fields', [])

        for field in additional_fields:
            if isinstance(field, dict) and field.get('code') == 'vatid':
                nif = field.get('value')
                if nif:
                    return str(nif).strip()

        return None

    except (json.JSONDecodeError, TypeError, AttributeError) as e:
        log(f"Erro ao extrair NIF: {str(e)}", "ERRO")
        return None

# Função para adicionar taxid ao customer JSON
def add_taxid_to_customer(customer_json, nif):
    """
    Adiciona o campo taxid ao JSON do customer.
    Se taxid já existir, não modifica.
    """
    try:
        if not customer_json:
            return None

        # Se customer_json for string, fazer parse JSON
        if isinstance(customer_json, str):
            customer_data = json.loads(customer_json)
        else:
            customer_data = customer_json

        # Verificar se taxid já existe
        if 'taxid' in customer_data:
            log("taxid já existe no customer, pulando...", "INFO")
            return None  # Não modificar se já existe

        # SEMPRE adicionar taxid e remover nif se existir
        if 'nif' in customer_data:
            log(f"Removendo campo 'nif' e adicionando 'taxid' (valor: {nif})", "INFO")
            del customer_data['nif']
        else:
            log(f"Adicionando taxid: {nif} ao customer", "INFO")

        customer_data['taxid'] = nif if nif else None

        # Retornar JSON atualizado como string
        return json.dumps(customer_data, ensure_ascii=False)

    except (json.JSONDecodeError, TypeError, AttributeError) as e:
        log(f"Erro ao processar customer JSON: {str(e)}", "ERRO")
        return None

# Definir variáveis para o Banco B (origem)
SSH_USER_B = "root"
SSH_PASS_B = "fF[8Q%C[WR]4!UDW"
SSH_HOST_B = "************"
SSH_PORT_B = 22
TUNNEL_PORT_B = 13311
DB_HOST_B = "127.0.0.1"
DB_PORT_B = 3306
DB_USER_B = "connect"
DB_PASS_B = "J%HR6Bj}w4lgQs@.GJ:Bnr~34-?10v^O"
DB_NAME_B = "connectbig"

# Definir variáveis para o Banco Accounts (destino)
DB_ACC_HOST = "**************"
DB_ACC_PORT = 3306
DB_ACC_USER = "hublord"
DB_ACC_PASS = "sYR9*z#G0fJ1,ONr4wS}xEka9WRad8r8"
DB_ACC_NAME = "db_accounts"

# IDs de marketplace a serem excluídos
EXCLUDED_MARKETPLACE_IDS = [59, 60, 61, 65, 66, 67]

# Status a serem excluídos
EXCLUDED_STATUS = ['REFUSED', 'CANCELED', 'WAITING_DEBIT_PAYMENT', 'WaitingAcceptance', 'CLOSED', 'STAGING', 'WAITING_ACCEPTANCE']

log("Iniciando script de consulta de orders")
log(f"Excluindo marketplace_ids: {EXCLUDED_MARKETPLACE_IDS}")
log(f"Excluindo status: {EXCLUDED_STATUS}")

try:
    log("Iniciando túnel SSH para o Banco B (origem)...")
    with SSHTunnelForwarder(
        (SSH_HOST_B, SSH_PORT_B),
        ssh_username=SSH_USER_B,
        ssh_password=SSH_PASS_B,
        remote_bind_address=(DB_HOST_B, DB_PORT_B),
        local_bind_address=("127.0.0.1", TUNNEL_PORT_B)
    ) as tunnel_B:
        log(f"Túnel SSH para Banco B estabelecido (localhost:{TUNNEL_PORT_B} -> {SSH_HOST_B}:{SSH_PORT_B})")

        # Variável para controlar estado da conexão
        connection_B = None

        try:
            log(f"Conectando ao banco de dados de origem (Banco B)...")
            connection_B = pymysql.connect(
                host="127.0.0.1",
                port=TUNNEL_PORT_B,
                user=DB_USER_B,
                password=DB_PASS_B,
                database=DB_NAME_B
            )
            log("Conexão com Banco B (origem) estabelecida com sucesso")

            cursor_B = connection_B.cursor()

            # Construir query para buscar orders excluindo marketplace_ids específicos
            log("Construindo query para buscar orders excluindo marketplace_ids específicos...")

            # Criar placeholders para os IDs excluídos
            excluded_placeholders = ','.join(['%s'] * len(EXCLUDED_MARKETPLACE_IDS))

            sql_query_B = f"""
                SELECT order_id, response_first
                FROM orders
                WHERE marketplace_id NOT IN ({excluded_placeholders})
                AND status != 'REFUSED'
                AND status != 'CANCELED'
                AND status != 'WAITING_DEBIT_PAYMENT'
                AND status != 'WaitingAcceptance'
                AND status != 'CLOSED'
                AND status != 'STAGING'
                AND status != 'WAITING_ACCEPTANCE'
                ORDER BY id DESC
            """

            log(f"Executando query no Banco B para buscar orders (excluindo marketplace_ids: {EXCLUDED_MARKETPLACE_IDS} e status: {EXCLUDED_STATUS})...")
            cursor_B.execute(sql_query_B, EXCLUDED_MARKETPLACE_IDS)
            results_B = cursor_B.fetchall()

            log(f"Encontrados {len(results_B)} registros na tabela orders")
            log(f"Quantidade total de orders (excluindo marketplace_ids {EXCLUDED_MARKETPLACE_IDS}): {len(results_B)}")

            # Processar dados para extrair NIFs
            # Conectar à base de dados A para atualizar customer
            log(f"Conectando ao banco de dados A (Accounts) em {DB_ACC_HOST}...")
            connection_acc = pymysql.connect(
                host=DB_ACC_HOST,
                port=DB_ACC_PORT,
                user=DB_ACC_USER,
                password=DB_ACC_PASS,
                database=DB_ACC_NAME
            )
            log("Conexão com Banco A (Accounts) estabelecida com sucesso")

            cursor_acc = connection_acc.cursor()

            log("Processando dados para extrair NIFs e atualizar customer...")

            # Processar apenas a primeira order com NIF que não tenha taxid para teste
            test_order_processed = False
            orders_checked = 0

            for row in results_B:
                if test_order_processed:
                    break  # Processar apenas uma order para teste

                orders_checked += 1
                if orders_checked > 10:  # Limitar busca a 10 orders para não demorar
                    log("Limite de busca atingido (10 orders), parando...")
                    break

                order_id, response_first = row
                nif = extract_nif_from_response(response_first)

                if nif:
                    log(f"Processando order_id: {order_id} com NIF: {nif}")

                    # Buscar na tabela tbl_orders da base A pelo campo reference
                    query_acc = """
                        SELECT id, reference, customer
                        FROM tbl_orders
                        WHERE reference = %s
                    """

                    cursor_acc.execute(query_acc, (order_id,))
                    result_acc = cursor_acc.fetchone()

                    if result_acc:
                        acc_id, acc_reference, customer_json = result_acc
                        log(f"Order encontrada na base A - ID: {acc_id}, Reference: {acc_reference}")

                        # Mostrar customer atual
                        log(f"Customer atual: {str(customer_json)[:200]}...")

                        # Verificar se já tem taxid
                        try:
                            current_customer = json.loads(customer_json) if isinstance(customer_json, str) else customer_json
                            if 'taxid' in current_customer:
                                log(f"Customer já tem taxid: {current_customer.get('taxid')}", "INFO")
                            else:
                                log("Customer não tem taxid, será adicionado", "INFO")
                        except:
                            pass

                        # Adicionar taxid ao customer
                        updated_customer = add_taxid_to_customer(customer_json, nif)

                        if updated_customer:
                            # Mostrar o que será gravado
                            log(f"Dados que serão gravados: {updated_customer}")

                            # Verificar se taxid está presente no JSON
                            try:
                                check_data = json.loads(updated_customer)
                                if 'taxid' in check_data:
                                    log(f"CONFIRMADO: taxid presente no JSON: {check_data['taxid']}", "SUCESSO")
                                else:
                                    log("ERRO: taxid NÃO está presente no JSON!", "ERRO")
                                    continue
                            except:
                                log("ERRO: Não foi possível verificar o JSON", "ERRO")
                                continue

                            # Atualizar apenas a coluna customer
                            update_query = """
                                UPDATE tbl_orders
                                SET customer = %s,
                                    updated_at = NOW()
                                WHERE id = %s
                            """

                            try:
                                cursor_acc.execute(update_query, (updated_customer, acc_id))
                                connection_acc.commit()
                                log(f"Customer atualizado com sucesso para order {acc_reference}", "SUCESSO")

                                # Verificar o que foi realmente gravado na base de dados
                                verify_query = "SELECT customer FROM tbl_orders WHERE id = %s"
                                cursor_acc.execute(verify_query, (acc_id,))
                                saved_customer = cursor_acc.fetchone()[0]
                                log(f"Verificação - Customer gravado na BD: {saved_customer}")

                                test_order_processed = True

                            except Exception as e:
                                log(f"Erro ao atualizar customer: {str(e)}", "ERRO")
                                connection_acc.rollback()
                        else:
                            log("Customer não foi modificado (taxid já existe ou erro)")
                            # Não marcar como processado, continuar procurando
                    else:
                        log(f"Order {order_id} não encontrada na base A")

            if not test_order_processed:
                log("Nenhuma order com NIF foi processada")

            # Fechar cursor da base A
            cursor_acc.close()
            connection_acc.close()
            log("Conexão com Banco A encerrada")

            # Fechar cursor
            cursor_B.close()

            log("Script executado com sucesso!")

        except Exception as e:
            log(f"Erro durante execução: {str(e)}", "ERRO")
            import traceback
            log(traceback.format_exc(), "ERRO")

        finally:
            # Fechar conexão corretamente
            if connection_B is not None and connection_B.open:
                try:
                    connection_B.close()
                    log("Conexão com Banco B encerrada")
                except Exception as e:
                    log(f"Erro ao fechar conexão B: {str(e)}", "ERRO")

            log("Conexão com o banco foi encerrada")

except Exception as e:
    log(f"Erro ao configurar túnel SSH: {str(e)}", "ERRO")
    import traceback
    log(traceback.format_exc(), "ERRO")

log("Script finalizado")