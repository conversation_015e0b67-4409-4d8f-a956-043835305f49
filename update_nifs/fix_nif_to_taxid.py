import pymysql
import json
from datetime import datetime

# Função para exibir logs com timestamp
def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

# Definir variáveis para o Banco Accounts
DB_ACC_HOST = "**************"
DB_ACC_PORT = 3306
DB_ACC_USER = "hublord"
DB_ACC_PASS = "sYR9*z#G0fJ1,ONr4wS}xEka9WRad8r8"
DB_ACC_NAME = "db_accounts"

log("Iniciando script para corrigir campo 'nif' para 'taxid'")

try:
    # Conectar à base de dados A
    log(f"Conectando ao banco de dados A (Accounts) em {DB_ACC_HOST}...")
    connection_acc = pymysql.connect(
        host=DB_ACC_HOST,
        port=DB_ACC_PORT,
        user=DB_ACC_USER,
        password=DB_ACC_PASS,
        database=DB_ACC_NAME
    )
    log("Conexão com Banco A (Accounts) estabelecida com sucesso")
    
    cursor_acc = connection_acc.cursor()
    
    # Buscar orders que tenham 'nif' no customer mas não tenham 'taxid'
    query = """
        SELECT id, reference, customer
        FROM tbl_orders
        WHERE customer LIKE '%"nif":%'
        AND customer NOT LIKE '%"taxid":%'
        LIMIT 5
    """
    
    cursor_acc.execute(query)
    results = cursor_acc.fetchall()
    
    log(f"Encontradas {len(results)} orders com campo 'nif' para corrigir")
    
    for row in results:
        acc_id, acc_reference, customer_json = row
        
        try:
            # Parse do JSON
            customer_data = json.loads(customer_json)
            
            # Verificar se tem 'nif' mas não tem 'taxid'
            if 'nif' in customer_data and 'taxid' not in customer_data:
                nif_value = customer_data['nif']
                
                # Remover o campo 'nif' incorreto
                del customer_data['nif']
                
                # Adicionar o campo 'taxid' correto
                customer_data['taxid'] = nif_value
                
                # Converter de volta para JSON
                updated_customer = json.dumps(customer_data, ensure_ascii=False)
                
                # Atualizar no banco
                update_query = """
                    UPDATE tbl_orders
                    SET customer = %s,
                        updated_at = NOW()
                    WHERE id = %s
                """
                
                cursor_acc.execute(update_query, (updated_customer, acc_id))
                connection_acc.commit()
                
                log(f"Order {acc_reference} corrigida: 'nif' -> 'taxid' = {nif_value}", "SUCESSO")
                
        except Exception as e:
            log(f"Erro ao processar order {acc_reference}: {str(e)}", "ERRO")
            connection_acc.rollback()
    
    # Fechar conexão
    cursor_acc.close()
    connection_acc.close()
    log("Conexão encerrada")
    
except Exception as e:
    log(f"Erro: {str(e)}", "ERRO")

log("Script finalizado")
