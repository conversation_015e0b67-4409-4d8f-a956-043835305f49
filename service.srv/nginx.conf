worker_processes 4;
pid /run/nginx.pid;
 
events {
  worker_connections  2048;
  multi_accept on;
  use epoll;
}
 
http {
  server_tokens off;
  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 15;
  types_hash_max_size 2048;
  include /etc/nginx/mime.types;
  default_type application/octet-stream;
  access_log on;
  error_log on;
  real_ip_header X-Real-IP;
  charset utf-8;
  gzip on;
  gzip_disable "msie6";
  include /etc/nginx/conf.d/*.conf;
  include /etc/nginx/sites-available/*;
  open_file_cache max=100;
}