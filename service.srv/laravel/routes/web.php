<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Redirecionar raiz para dashboard
Route::get('/', function () {
    return redirect('/dashboard');
});

// Rotas para o dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');
Route::get('/dashboard/orders', [DashboardController::class, 'getOrders'])->name('dashboard.orders');
Route::post('/dashboard/process-form', [DashboardController::class, 'processForm'])->name('dashboard.process-form');
Route::post('/dashboard/toggle-debug', [DashboardController::class, 'toggleDebug'])->name('dashboard.toggle-debug');
Route::post('/dashboard/search-order', [DashboardController::class, 'searchOrder'])->name('dashboard.search-order');
Route::post('/dashboard/search-timeline', [DashboardController::class, 'searchTimeline'])->name('dashboard.search-timeline');
Route::get('/dashboard/order-details/{orderId}', [DashboardController::class, 'getOrderDetails'])->name('dashboard.order-details');
