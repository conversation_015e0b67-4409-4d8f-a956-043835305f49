<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;  // Importação de Log
use Illuminate\Support\Facades\Session;

class LanguageMiddleware
{
    public function handle($request, Closure $next)
    {
        // Verifica o idioma na URL ou sessão
        $locale = $request->query('lang', Session::get('locale', 'en'));

        // Define o idioma se for válido
        if (in_array($locale, ['en', 'pt', 'es', 'fr'])) {
            App::setLocale($locale);
            Session::put('locale', $locale);
        }

        // Teste de depuração
        \dd('Idioma atual: ' . App::getLocale());

        return $next($request);
    }

    public function handle_log($request, Closure $next)
{
    // Verifica o idioma na URL ou sessão
    $locale = $request->query('lang', Session::get('locale', config('app.locale')));

    // Define o idioma se for válido
    if (in_array($locale, ['en', 'pt', 'es', 'fr'])) {
        App::setLocale($locale);
        Session::put('locale', $locale);
    }

    // Registra o idioma atual no log
    Log::info('Idioma atual: ' . App::getLocale());

    return $next($request);
}
}
