<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class OrdersApi extends Model
{
    const URL_DATA_ORDERS = 'https://connect-big.bighub.store/api/orders/user/';
    const URL_MARKETPLACES = 'https://connect-big.bighub.store/api/marketplace/list';
    const BEAR = 'bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd';

    /**
     * Busca pedidos sem usuário via API
     *
     * @param int $id
     * @return object|null
     */
    public function curl_get_orders_no_user($id)
    {
        $url_api = self::URL_DATA_ORDERS . $id;
        $handle = curl_init($url_api);

        $request_headers = [];
        $request_headers[] = 'Content-Type: application/json';
        $request_headers[] = 'Authorization: Bearer ' . self::BEAR;

        curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
        curl_setopt($handle, CURLOPT_TIMEOUT, 30);
        curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 10);

        $response = curl_exec($handle);
        $httpCode = curl_getinfo($handle, CURLINFO_HTTP_CODE);

        if (curl_errno($handle)) {
            Log::error('Erro cURL ao buscar pedidos: ' . curl_error($handle));
            curl_close($handle);
            return null;
        }

        curl_close($handle);

        if ($httpCode !== 200) {
            Log::error("Erro HTTP {$httpCode} ao buscar pedidos para ID: {$id}");
            return null;
        }

        return json_decode($response);
    }

    /**
     * Busca lista de marketplaces via API
     *
     * @return array
     */
    public function curl_get_marketplaces()
    {
        $url_api = self::URL_MARKETPLACES;
        $handle = curl_init($url_api);

        $request_headers = [];
        $request_headers[] = 'Content-Type: application/json';
        $request_headers[] = 'Authorization: Bearer ' . self::BEAR;

        curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
        curl_setopt($handle, CURLOPT_TIMEOUT, 30);
        curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 10);

        $response = curl_exec($handle);
        $httpCode = curl_getinfo($handle, CURLINFO_HTTP_CODE);

        if (curl_errno($handle)) {
            Log::error('Erro cURL ao buscar marketplaces: ' . curl_error($handle));
            curl_close($handle);
            return [];
        }

        curl_close($handle);

        if ($httpCode !== 200) {
            Log::error("Erro HTTP {$httpCode} ao buscar marketplaces");
            return [];
        }

        $data = json_decode($response, true);
        return $data ?? [];
    }

    /**
     * Processa os pedidos e organiza por tabelas separadas
     *
     * @param bool $debug
     * @return array
     */
    public function processOrders($debug = false)
    {
        // Sempre buscar ambas as ordens (25 e 33)
        $orders_no_user_support = $this->curl_get_orders_no_user(25);
        $orders_no_user = $this->curl_get_orders_no_user(33);

        if ($debug) {
            Log::info("Validando orders modo debug - ATIVO - Equipe Desenvolvimento.");
        } else {
            Log::info("Validando orders modo produção.");
        }

        // Arrays para as tabelas
        $orders_25 = [];
        $orders_33 = [];
        $failed = 0;

        // Arrays para estatísticas por status
        $waiting_acceptance = [];
        $waiting_debit_payment = [];
        $staging = [];
        $created = [];

        // Data limite para filtrar orders 33 (01/04/2024)
        $dateLimit = '2024-04-01';

        // Processar pedidos de suporte (ID 25)
        if (!is_null($orders_no_user_support) && isset($orders_no_user_support->data)) {
            foreach ($orders_no_user_support->data as $order) {
                if (empty($order)) {
                    $failed++;
                    continue;
                }

                $orderData = [
                    'marketplace_id' => $order->marketplace->id ?? 'N/A',
                    'user_id' => $order->user_id ?? 'N/A',
                    'order_id' => $order->order_id,
                    'status' => $order->status,
                    'channel' => $order->channel->code ?? 'N/A',
                    'price' => $order->price,
                    'product_quantity' => $order->product_quantity ?? 1,
                    'order_created_at' => $order->order_created_at
                ];

                $orders_25[] = $orderData;

                // Categorizar por status para estatísticas
                $this->categorizeByStatus($order->status, $orderData, $waiting_acceptance, $waiting_debit_payment, $staging, $created);
            }
        }

        // Processar pedidos principais (ID 33)
        if (!is_null($orders_no_user) && isset($orders_no_user->data)) {
            foreach ($orders_no_user->data as $order) {
                if (empty($order)) {
                    $failed++;
                    continue;
                }

                // Filtrar apenas orders com created_at >= 01/04/2024
                $orderDate = date('Y-m-d', strtotime($order->created_at));
                if ($orderDate < $dateLimit) {
                    continue;
                }

                $orderData = [
                    'marketplace_id' => $order->marketplace->id ?? 'N/A',
                    'user_id' => $order->user_id ?? 'N/A',
                    'order_id' => $order->order_id,
                    'status' => $order->status,
                    'channel' => $order->channel->code ?? 'N/A',
                    'price' => $order->price,
                    'product_quantity' => $order->product_quantity ?? 1,
                    'order_created_at' => $order->order_created_at
                ];

                $orders_33[] = $orderData;

                // Categorizar por status para estatísticas
                $this->categorizeByStatus($order->status, $orderData, $waiting_acceptance, $waiting_debit_payment, $staging, $created);
            }
        } else {
            Log::warning('Nenhum pedido encontrado ou resposta inválida.');
        }

        // Ordenar por data de criação (mais recentes primeiro)
        usort($orders_25, function($a, $b) {
            return strtotime($b['order_created_at']) - strtotime($a['order_created_at']);
        });

        usort($orders_33, function($a, $b) {
            return strtotime($b['order_created_at']) - strtotime($a['order_created_at']);
        });

        return [
            'message' => 'Processamento concluído',
            'orders_25' => $orders_25,
            'orders_33' => $orders_33,
            'failed' => $failed,
            'total_25' => count($orders_25),
            'total_33' => count($orders_33),
            'waiting_acceptance' => $waiting_acceptance,
            'waiting_debit_payment' => $waiting_debit_payment,
            'staging' => $staging,
            'created' => $created,
            'debug_mode' => $debug
        ];
    }

    /**
     * Categoriza ordem por status
     *
     * @param string $status
     * @param array $orderData
     * @param array &$waiting_acceptance
     * @param array &$waiting_debit_payment
     * @param array &$staging
     * @param array &$created
     */
    private function categorizeByStatus($status, $orderData, &$waiting_acceptance, &$waiting_debit_payment, &$staging, &$created)
    {
        switch (strtolower($status)) {
            case 'waiting_acceptance':
            case 'waitingacceptance':
                $waiting_acceptance[] = $orderData;
                break;
            case 'waiting_debit_payment':
            case 'waiting_debit':
                $waiting_debit_payment[] = $orderData;
                break;
            case 'staging':
                $staging[] = $orderData;
                break;
            case 'created':
                $created[] = $orderData;
                break;
        }
    }

    /**
     * Explora o código do canal para extrair marketplace e código
     *
     * @param string $code
     * @return array
     */
    private function exploreCode($code)
    {
        // Implementação básica - ajuste conforme sua lógica
        $parts = explode('-', $code);

        return [
            'mkt' => $parts[0] ?? 'Unknown',
            'code' => $parts[1] ?? $code
        ];
    }

    /**
     * Busca detalhes de uma order específica via API
     *
     * @param string $orderId
     * @return array|null
     */
    public static function getOrderDetails($orderId)
    {
        try {
            Log::info("Buscando detalhes da order: {$orderId}");

            $url = "https://connect-big.bighub.store/api/orders/order/{$orderId}";

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . self::BEAR,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ])->get($url);

            if ($response->successful()) {
                $data = $response->json();
                Log::info("Detalhes da order {$orderId} obtidos com sucesso");
                return $data;
            } else {
                Log::error("Erro ao buscar detalhes da order {$orderId}: " . $response->status() . " - " . $response->body());
                return null;
            }

        } catch (\Exception $e) {
            Log::error("Exceção ao buscar detalhes da order {$orderId}: " . $e->getMessage());
            return null;
        }
    }
}
