<?php

namespace App;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CandlesController
{
    /**
     * Retorna os dados mais recentes da tabela last_candles
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function latest()
    {
        try {
            // Buscar o registro mais recente da tabela last_candles
            $latestCandle = DB::table('last_candles')
                ->orderBy('id', 'desc')
                ->first();

            if (!$latestCandle) {
                return response()->json(['error' => 'Nenhum dado encontrado'], 404);
            }

            return response()->json($latestCandle);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Retorna os últimos 100 registros da tabela last_candles
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function last50()
    {
        try {
            // Buscar os últimos 100 registros da tabela last_candles
            $candles = DB::table('last_candles')
                ->orderBy('id', 'desc')
                ->limit(100)
                ->get();

            if ($candles->isEmpty()) {
                return response()->json(['error' => 'Nenhum dado encontrado'], 404);
            }

            // Processar os dados para extrair as informações de candles
            $processedCandles = [];
            foreach ($candles as $candle) {
                $candleData = json_decode($candle->last_json, true);
                if ($candleData) {
                    $processedCandles[] = [
                        'id' => $candle->id,
                        'data_hora' => $candleData['data_hora'] ?? null,
                        'timeframe' => $candleData['timeframe'] ?? null,
                        'ativo' => $candleData['ativo'] ?? null,
                        'green' => $candleData['contagem']['green'] ?? 0,
                        'red' => $candleData['contagem']['red'] ?? 0,
                        'doji' => $candleData['contagem']['doji'] ?? 0,
                        'total_velas' => $candleData['total_velas'] ?? 0,
                        'timestamp' => $candleData['timestamp'] ?? null
                    ];
                }
            }

            return response()->json($processedCandles);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
